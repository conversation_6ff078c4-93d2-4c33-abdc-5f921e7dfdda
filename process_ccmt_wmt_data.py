#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CCMT&WMT2023 Chinese-English Parallel Data Preprocessing Script
处理 CCMT&WMT2023 中英文平行语料数据预处理脚本

This script processes the CCMT&WMT2023 dataset to create clean, aligned parallel text files
for machine translation training.

Usage:
    python process_ccmt_wmt_data.py

Output:
    - train_zh.txt: Chinese training sentences
    - train_en.txt: English training sentences  
    - dev_zh.txt: Chinese development sentences
    - dev_en.txt: English development sentences
"""

import os
import pandas as pd
import numpy as np
import re
import csv
import sys
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CCMTDataProcessor:
    def __init__(self, data_root="CCMT&WMT2023.ch-en"):
        """
        初始化数据处理器
        
        Args:
            data_root (str): 数据集根目录路径
        """
        self.data_root = Path(data_root)
        self.train_dir = self.data_root / "train" / "parallel"
        self.dev_dir = self.data_root / "dev"
        
        # 输出文件路径
        self.output_dir = Path("data")
        self.output_dir.mkdir(exist_ok=True)
        
        # 训练数据中的平行语料文件对
        self.parallel_files = [
            # CASIA2015
            ("casia2015/casia2015_ch.txt", "casia2015/casia2015_en.txt"),
            # CASICT2011
            ("casict2011/casict-A_ch.txt", "casict2011/casict-A_en.txt"),
            ("casict2011/casict-B_ch.txt", "casict2011/casict-B_en.txt"),
            # CASICT2015
            ("casict2015/casict2015_ch.txt", "casict2015/casict2015_en.txt"),
            # Datum2015
            ("datum2015/datum_ch.txt", "datum2015/datum_en.txt"),
            # NEU2017
            ("neu2017/NEU_cn.txt", "neu2017/NEU_en.txt"),
        ]
        
        # Datum2017 有多个文件
        for i in range(1, 21):
            self.parallel_files.append((
                f"datum2017/Book{i}_cn.txt", 
                f"datum2017/Book{i}_en.txt"
            ))
    
    def read_parallel_file_pair(self, zh_file, en_file):
        """
        读取一对平行语料文件
        
        Args:
            zh_file (str): 中文文件路径
            en_file (str): 英文文件路径
            
        Returns:
            tuple: (中文句子列表, 英文句子列表)
        """
        zh_path = self.train_dir / zh_file
        en_path = self.train_dir / en_file
        
        if not zh_path.exists() or not en_path.exists():
            logger.warning(f"文件不存在: {zh_path} 或 {en_path}")
            return [], []
        
        try:
            # 读取中文文件
            with open(zh_path, 'r', encoding='utf-8') as f:
                zh_lines = [line.strip() for line in f if line.strip()]
            
            # 读取英文文件
            with open(en_path, 'r', encoding='utf-8') as f:
                en_lines = [line.strip() for line in f if line.strip()]
            
            # 检查行数是否匹配
            if len(zh_lines) != len(en_lines):
                logger.warning(f"文件行数不匹配: {zh_file} ({len(zh_lines)}) vs {en_file} ({len(en_lines)})")
                # 取较小的长度以保持对齐
                min_len = min(len(zh_lines), len(en_lines))
                zh_lines = zh_lines[:min_len]
                en_lines = en_lines[:min_len]
            
            logger.info(f"成功读取 {zh_file}: {len(zh_lines)} 句对")
            return zh_lines, en_lines
            
        except Exception as e:
            logger.error(f"读取文件出错 {zh_file}, {en_file}: {e}")
            return [], []
    
    def clean_text(self, text):
        """
        清理文本数据
        
        Args:
            text (str): 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text or not isinstance(text, str):
            return ""
        
        # 移除HTML标签
        text = re.sub(r'<.*?>', ' ', text)
        text = re.sub(r'&lt;.*?&gt;', ' ', text)
        text = re.sub(r'&?(amp|nbsp|quot);', ' ', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text)
        
        # 去除首尾空格
        text = text.strip()
        
        return text
    
    def filter_sentence_pair(self, zh_sent, en_sent):
        """
        过滤句子对
        
        Args:
            zh_sent (str): 中文句子
            en_sent (str): 英文句子
            
        Returns:
            bool: 是否保留该句子对
        """
        # 清理文本
        zh_sent = self.clean_text(zh_sent)
        en_sent = self.clean_text(en_sent)
        
        # 检查是否为空
        if not zh_sent or not en_sent:
            return False
        
        # 检查长度
        if len(zh_sent) < 3 or len(en_sent) < 3:
            return False
        
        if len(zh_sent) > 500 or len(en_sent) > 500:
            return False
        
        # 检查长度比例
        zh_words = len(zh_sent)
        en_words = len(en_sent.split())
        
        if zh_words > 0 and en_words > 0:
            ratio = max(zh_words, en_words) / min(zh_words, en_words)
            if ratio > 3:  # 长度比例不能超过3:1
                return False
        
        # 检查是否相同（复制源文本）
        if zh_sent == en_sent:
            return False
        
        return True
    
    def collect_training_data(self):
        """
        收集所有训练数据
        
        Returns:
            tuple: (中文句子列表, 英文句子列表)
        """
        all_zh_sentences = []
        all_en_sentences = []
        
        logger.info("开始收集训练数据...")
        
        for zh_file, en_file in self.parallel_files:
            zh_lines, en_lines = self.read_parallel_file_pair(zh_file, en_file)
            
            # 过滤句子对
            filtered_zh = []
            filtered_en = []
            
            for zh_sent, en_sent in zip(zh_lines, en_lines):
                if self.filter_sentence_pair(zh_sent, en_sent):
                    filtered_zh.append(self.clean_text(zh_sent))
                    filtered_en.append(self.clean_text(en_sent))
            
            logger.info(f"{zh_file}: 原始 {len(zh_lines)} 句对，过滤后 {len(filtered_zh)} 句对")
            
            all_zh_sentences.extend(filtered_zh)
            all_en_sentences.extend(filtered_en)
        
        logger.info(f"总共收集到 {len(all_zh_sentences)} 个训练句对")
        return all_zh_sentences, all_en_sentences
    
    def collect_dev_data(self):
        """
        收集开发集数据
        
        Returns:
            tuple: (中文句子列表, 英文句子列表)
        """
        logger.info("开始收集开发集数据...")
        
        # 使用 CCMT2024CE 作为开发集
        dev_zh_file = self.dev_dir / "CCMT2024CE" / "2024_ce.zh"
        dev_en_file = self.dev_dir / "CCMT2024CE" / "2024_ce.en"
        
        if not dev_zh_file.exists() or not dev_en_file.exists():
            logger.warning("开发集文件不存在，将从训练数据中分割")
            return [], []
        
        try:
            with open(dev_zh_file, 'r', encoding='utf-8') as f:
                zh_lines = [line.strip() for line in f if line.strip()]
            
            with open(dev_en_file, 'r', encoding='utf-8') as f:
                en_lines = [line.strip() for line in f if line.strip()]
            
            # 确保对齐
            min_len = min(len(zh_lines), len(en_lines))
            zh_lines = zh_lines[:min_len]
            en_lines = en_lines[:min_len]
            
            # 过滤
            filtered_zh = []
            filtered_en = []
            
            for zh_sent, en_sent in zip(zh_lines, en_lines):
                if self.filter_sentence_pair(zh_sent, en_sent):
                    filtered_zh.append(self.clean_text(zh_sent))
                    filtered_en.append(self.clean_text(en_sent))
            
            logger.info(f"开发集: 原始 {len(zh_lines)} 句对，过滤后 {len(filtered_zh)} 句对")
            return filtered_zh, filtered_en
            
        except Exception as e:
            logger.error(f"读取开发集出错: {e}")
            return [], []

    def remove_duplicates(self, zh_sentences, en_sentences):
        """
        去除重复的句子对

        Args:
            zh_sentences (list): 中文句子列表
            en_sentences (list): 英文句子列表

        Returns:
            tuple: 去重后的句子列表
        """
        logger.info("开始去重...")

        # 创建DataFrame进行去重
        df = pd.DataFrame({
            'zh': zh_sentences,
            'en': en_sentences
        })

        original_count = len(df)

        # 去除重复行
        df = df.drop_duplicates()

        # 去除源文本和目标文本相同的行
        df = df[df['zh'] != df['en']]

        final_count = len(df)
        logger.info(f"去重: 原始 {original_count} 句对，去重后 {final_count} 句对")

        return df['zh'].tolist(), df['en'].tolist()

    def shuffle_data(self, zh_sentences, en_sentences):
        """
        随机打乱数据

        Args:
            zh_sentences (list): 中文句子列表
            en_sentences (list): 英文句子列表

        Returns:
            tuple: 打乱后的句子列表
        """
        logger.info("随机打乱数据...")

        # 创建索引并打乱
        indices = list(range(len(zh_sentences)))
        np.random.shuffle(indices)

        # 按照打乱的索引重新排列
        shuffled_zh = [zh_sentences[i] for i in indices]
        shuffled_en = [en_sentences[i] for i in indices]

        return shuffled_zh, shuffled_en

    def save_data(self, zh_sentences, en_sentences, prefix):
        """
        保存数据到文件

        Args:
            zh_sentences (list): 中文句子列表
            en_sentences (list): 英文句子列表
            prefix (str): 文件前缀 (train 或 dev)
        """
        zh_file = self.output_dir / f"{prefix}_zh.txt"
        en_file = self.output_dir / f"{prefix}_en.txt"

        # 保存中文文件
        with open(zh_file, 'w', encoding='utf-8') as f:
            for sentence in zh_sentences:
                f.write(sentence + '\n')

        # 保存英文文件
        with open(en_file, 'w', encoding='utf-8') as f:
            for sentence in en_sentences:
                f.write(sentence + '\n')

        logger.info(f"保存 {prefix} 数据: {len(zh_sentences)} 句对")
        logger.info(f"  中文文件: {zh_file}")
        logger.info(f"  英文文件: {en_file}")

    def process(self):
        """
        执行完整的数据处理流程
        """
        logger.info("开始处理 CCMT&WMT2023 数据集...")

        # 1. 收集训练数据
        train_zh, train_en = self.collect_training_data()

        if not train_zh:
            logger.error("没有收集到训练数据！")
            return

        # 2. 去重
        train_zh, train_en = self.remove_duplicates(train_zh, train_en)

        # 3. 随机打乱
        train_zh, train_en = self.shuffle_data(train_zh, train_en)

        # 4. 收集开发集数据
        dev_zh, dev_en = self.collect_dev_data()

        # 如果没有开发集，从训练数据中分割
        if not dev_zh:
            logger.info("从训练数据中分割开发集...")
            dev_size = min(5000, len(train_zh) // 10)  # 取10%或5000，取较小值

            dev_zh = train_zh[:dev_size]
            dev_en = train_en[:dev_size]
            train_zh = train_zh[dev_size:]
            train_en = train_en[dev_size:]

            logger.info(f"分割后: 训练集 {len(train_zh)} 句对，开发集 {len(dev_zh)} 句对")

        # 5. 保存数据
        self.save_data(train_zh, train_en, "train")
        self.save_data(dev_zh, dev_en, "dev")

        # 6. 输出统计信息
        logger.info("=" * 50)
        logger.info("数据处理完成！")
        logger.info(f"训练集: {len(train_zh)} 句对")
        logger.info(f"开发集: {len(dev_zh)} 句对")
        logger.info(f"总计: {len(train_zh) + len(dev_zh)} 句对")
        logger.info("=" * 50)

        # 显示样例
        if train_zh:
            logger.info("训练集样例:")
            for i in range(min(3, len(train_zh))):
                logger.info(f"  中文: {train_zh[i]}")
                logger.info(f"  英文: {train_en[i]}")
                logger.info("")

def main():
    """主函数"""
    # 设置随机种子以确保可重现性
    np.random.seed(42)

    # 创建数据处理器并执行处理
    processor = CCMTDataProcessor()
    processor.process()

if __name__ == "__main__":
    main()
