# CCMT&WMT2023 数据预处理指南

## 概述

这个工具用于处理 CCMT&WMT2023 中英文平行语料数据集，生成用于机器翻译训练的清洁、对齐的文本文件。

## 输出文件

处理完成后，将在 `data/` 目录下生成以下4个文件：

- `train_zh.txt` - 中文训练句子（每行一句）
- `train_en.txt` - 英文训练句子（与中文对齐）
- `dev_zh.txt` - 中文开发句子（每行一句）
- `dev_en.txt` - 英文开发句子（与中文对齐）

## 运行方法

### 方法1：使用简化脚本（推荐）

```bash
python run_data_processing.py
```

### 方法2：直接运行主脚本

```bash
python process_ccmt_wmt_data.py
```

### 方法3：使用现有的filter.py脚本

如果你想使用原有的过滤脚本：

```bash
cd OnMT0529/preprocess
python filter.py --source_path ../../CCMT&WMT2023.ch-en/2023 --source_lan zh --target_lan en
```

## 数据处理流程

1. **数据收集**: 从训练目录中读取所有平行语料文件
2. **数据清理**: 移除HTML标签、多余空格等
3. **数据过滤**: 
   - 移除空行
   - 移除过短（<3字符）或过长（>500字符）的句子
   - 移除长度比例异常的句子对（>3:1）
   - 移除源文本和目标文本相同的句子
4. **去重**: 移除重复的句子对
5. **数据分割**: 分离训练集和开发集
6. **随机打乱**: 随机化训练数据顺序
7. **保存**: 输出对齐的文本文件

## 处理的数据源

### 训练数据来源：
- CASIA2015: `casia2015/casia2015_ch.txt` & `casia2015/casia2015_en.txt`
- CASICT2011: `casict2011/casict-A_ch.txt` & `casict2011/casict-A_en.txt`
- CASICT2011: `casict2011/casict-B_ch.txt` & `casict2011/casict-B_en.txt`
- CASICT2015: `casict2015/casict2015_ch.txt` & `casict2015/casict2015_en.txt`
- Datum2015: `datum2015/datum_ch.txt` & `datum2015/datum_en.txt`
- NEU2017: `neu2017/NEU_cn.txt` & `neu2017/NEU_en.txt`
- Datum2017: `datum2017/Book1_cn.txt` 到 `datum2017/Book20_cn.txt` 及对应英文文件

### 开发数据来源：
- CCMT2024CE: `dev/CCMT2024CE/2024_ce.zh` & `dev/CCMT2024CE/2024_ce.en`

## 依赖要求

```bash
pip install pandas numpy
```

## 故障排除

### 1. 文件编码问题
如果遇到编码错误，确保所有文本文件都是UTF-8编码。

### 2. 文件不存在
脚本会自动跳过不存在的文件并记录警告。检查数据目录结构是否正确。

### 3. 内存不足
如果数据量很大导致内存不足，可以修改脚本分批处理数据。

### 4. 行数不匹配
脚本会自动处理中英文文件行数不匹配的情况，取较小的行数以保持对齐。

## 数据质量检查

处理完成后，可以检查：

1. **文件行数**: 确保中英文文件行数相同
```bash
wc -l data/train_zh.txt data/train_en.txt
wc -l data/dev_zh.txt data/dev_en.txt
```

2. **数据样例**: 查看前几行确保对齐正确
```bash
head -5 data/train_zh.txt
head -5 data/train_en.txt
```

3. **数据统计**: 查看处理日志中的统计信息

## 自定义配置

如需修改处理参数，可以编辑 `process_ccmt_wmt_data.py` 中的以下设置：

- `filter_sentence_pair()`: 修改过滤条件
- `collect_dev_data()`: 修改开发集来源
- 开发集大小: 修改 `dev_size = min(5000, len(train_zh) // 10)`

## 后续步骤

数据预处理完成后，可以：

1. 使用生成的文件训练机器翻译模型
2. 进行分词处理（如使用SentencePiece）
3. 转换为模型训练所需的格式

## 联系支持

如果遇到问题，请检查：
1. 数据目录结构是否正确
2. 依赖包是否已安装
3. 文件权限是否正确
4. 查看处理日志中的错误信息
