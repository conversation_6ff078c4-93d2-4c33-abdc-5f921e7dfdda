#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据验证脚本
Data verification script

验证生成的平行语料数据的质量和对齐情况
"""

import os
from pathlib import Path

def verify_alignment(zh_file, en_file):
    """验证中英文文件的对齐情况"""
    print(f"验证文件对齐: {zh_file} <-> {en_file}")
    
    with open(zh_file, 'r', encoding='utf-8') as f:
        zh_lines = f.readlines()
    
    with open(en_file, 'r', encoding='utf-8') as f:
        en_lines = f.readlines()
    
    zh_count = len(zh_lines)
    en_count = len(en_lines)
    
    print(f"  中文行数: {zh_count}")
    print(f"  英文行数: {en_count}")
    
    if zh_count == en_count:
        print("  ✓ 行数对齐正确")
    else:
        print("  ✗ 行数不对齐！")
        return False
    
    # 检查前几行的对齐情况
    print("  前3行样例:")
    for i in range(min(3, zh_count)):
        zh_line = zh_lines[i].strip()
        en_line = en_lines[i].strip()
        print(f"    {i+1}. 中文: {zh_line[:50]}{'...' if len(zh_line) > 50 else ''}")
        print(f"       英文: {en_line[:50]}{'...' if len(en_line) > 50 else ''}")
        print()
    
    return True

def check_data_quality(file_path, lang):
    """检查数据质量"""
    print(f"检查数据质量: {file_path} ({lang})")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    total_lines = len(lines)
    empty_lines = 0
    short_lines = 0
    long_lines = 0
    
    for line in lines:
        line = line.strip()
        if not line:
            empty_lines += 1
        elif len(line) < 3:
            short_lines += 1
        elif len(line) > 500:
            long_lines += 1
    
    print(f"  总行数: {total_lines}")
    print(f"  空行数: {empty_lines}")
    print(f"  过短行数 (<3字符): {short_lines}")
    print(f"  过长行数 (>500字符): {long_lines}")
    
    if empty_lines == 0 and short_lines == 0:
        print("  ✓ 数据质量良好")
    else:
        print("  ⚠ 数据质量需要注意")
    
    print()

def main():
    """主函数"""
    print("CCMT&WMT2023 数据验证工具")
    print("=" * 40)
    
    data_dir = Path("data")
    
    if not data_dir.exists():
        print("✗ data/ 目录不存在")
        return 1
    
    # 检查文件是否存在
    files = {
        'train_zh': data_dir / "train_zh.txt",
        'train_en': data_dir / "train_en.txt",
        'dev_zh': data_dir / "dev_zh.txt",
        'dev_en': data_dir / "dev_en.txt"
    }
    
    missing_files = []
    for name, path in files.items():
        if not path.exists():
            missing_files.append(name)
    
    if missing_files:
        print(f"✗ 缺少文件: {', '.join(missing_files)}")
        return 1
    
    print("✓ 所有文件都存在")
    print()
    
    # 验证对齐
    print("1. 验证文件对齐")
    print("-" * 20)
    
    train_aligned = verify_alignment(files['train_zh'], files['train_en'])
    print()
    
    dev_aligned = verify_alignment(files['dev_zh'], files['dev_en'])
    print()
    
    # 检查数据质量
    print("2. 检查数据质量")
    print("-" * 20)
    
    check_data_quality(files['train_zh'], "中文")
    check_data_quality(files['train_en'], "英文")
    check_data_quality(files['dev_zh'], "中文")
    check_data_quality(files['dev_en'], "英文")
    
    # 总结
    print("3. 验证总结")
    print("-" * 20)
    
    if train_aligned and dev_aligned:
        print("✓ 所有文件对齐正确")
        print("✓ 数据可以用于机器翻译训练")
        
        # 显示文件大小
        print("\n文件信息:")
        for name, path in files.items():
            size_mb = path.stat().st_size / (1024 * 1024)
            print(f"  {name}: {size_mb:.1f} MB")
        
        return 0
    else:
        print("✗ 存在对齐问题，请检查数据处理过程")
        return 1

if __name__ == "__main__":
    exit(main())
