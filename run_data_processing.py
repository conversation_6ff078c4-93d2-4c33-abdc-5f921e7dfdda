#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的数据处理运行脚本
Simplified data processing runner script

这个脚本提供了一个简化的接口来运行数据预处理
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    try:
        import pandas
        import numpy
        print("✓ 依赖包检查通过")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install pandas numpy")
        return False

def check_data_structure():
    """检查数据结构"""
    data_root = Path("CCMT&WMT2023.ch-en")
    
    if not data_root.exists():
        print(f"✗ 数据目录不存在: {data_root}")
        return False
    
    train_dir = data_root / "train" / "parallel"
    if not train_dir.exists():
        print(f"✗ 训练数据目录不存在: {train_dir}")
        return False
    
    # 检查一些关键文件
    key_files = [
        "casia2015/casia2015_ch.txt",
        "casict2011/casict-A_ch.txt", 
        "datum2015/datum_ch.txt"
    ]
    
    found_files = 0
    for file_path in key_files:
        if (train_dir / file_path).exists():
            found_files += 1
    
    if found_files == 0:
        print("✗ 没有找到任何训练数据文件")
        return False
    
    print(f"✓ 数据结构检查通过 (找到 {found_files}/{len(key_files)} 个关键文件)")
    return True

def run_processing():
    """运行数据处理"""
    try:
        from process_ccmt_wmt_data import CCMTDataProcessor
        
        print("开始数据处理...")
        processor = CCMTDataProcessor()
        processor.process()
        
        return True
    except Exception as e:
        print(f"✗ 数据处理失败: {e}")
        return False

def main():
    """主函数"""
    print("CCMT&WMT2023 数据预处理工具")
    print("=" * 40)
    
    # 1. 检查依赖
    if not check_dependencies():
        return 1
    
    # 2. 检查数据结构
    if not check_data_structure():
        return 1
    
    # 3. 运行处理
    if not run_processing():
        return 1
    
    print("\n" + "=" * 40)
    print("数据处理完成！")
    print("输出文件位于 data/ 目录:")
    print("  - train_zh.txt: 中文训练数据")
    print("  - train_en.txt: 英文训练数据")
    print("  - dev_zh.txt: 中文开发数据")
    print("  - dev_en.txt: 英文开发数据")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
